# 地震时程分析滞回曲线绘制指南

## 概述

本模块提供专业的滞回曲线绘制功能，用于分析地震时程分析过程中的结构响应特性。主要包括：

1. **支座滞回曲线**：力-位移关系，反映支座的耗能特性
2. **桥墩滞回曲线**：弯矩-曲率关系，反映桥墩的塑性发展

## 主要特性

### 支座分析
- 支持多种支座类型：橡胶支座、摩擦支座、双线性支座
- 考虑轴向荷载对水平刚度的影响
- 自动计算滞回特性参数（初始刚度、能量耗散、等效阻尼比）
- 专业的工程单位显示

### 桥墩分析
- 弯矩-曲率滞回曲线
- 支持X、Y两个方向的分析
- 能量耗散时程分析
- 塑性发展评估

## 使用方法

### 1. 基本使用

```python
from utils.hysteresis_plotter import plot_seismic_hysteresis
from params import BridgeParams
from core.simply_supported_beam_model import SimplySupportedBeamModel

# 创建桥梁模型
params = BridgeParams()
model = SimplySupportedBeamModel(params)

# 绘制滞回曲线（需要先完成地震时程分析）
plot_seismic_hysteresis(
    results_dir='results',
    bearing_idx=0,           # 第一个支座
    pier_id='pier_x15.0_y0.0',  # 指定桥墩
    model=model
)
```

### 2. 高级使用

```python
from utils.hysteresis_plotter import HysteresisPlotter

# 创建绘图器
plotter = HysteresisPlotter('results')

# 加载数据
plotter.load_bearing_data(model=model)
plotter.load_pier_data('pier_x15.0_y0.0')

# 绘制支座滞回曲线
plotter.plot_bearing_hysteresis(
    bearing_idx=0,
    model=model,
    bearing_type='bilinear',  # 指定支座类型
    save_path='bearing_hysteresis.png'
)

# 绘制桥墩滞回曲线
plotter.plot_pier_hysteresis(
    pier_id='pier_x15.0_y0.0',
    direction='x',
    save_path='pier_hysteresis.png'
)

# 绘制综合图
plotter.plot_comprehensive_hysteresis(
    bearing_idx=0,
    pier_id='pier_x15.0_y0.0',
    model=model
)
```

## 支座类型说明

### 1. 橡胶支座 (rubber)
- 双线性本构关系
- 参数：初始刚度、屈服位移、屈服后刚度比

### 2. 摩擦支座 (friction)
- 考虑静摩擦和动摩擦
- 参数：摩擦系数、轴向荷载、速度阈值

### 3. 双线性支座 (bilinear)
- 考虑轴向荷载对刚度的影响
- 参数：基础刚度、轴向荷载影响系数

### 4. 自动选择 (auto)
- 根据模型参数自动选择合适的支座类型

## 输出文件

### 图形文件
- `bearing_{idx}_hysteresis.png`：支座滞回曲线
- `pier_{pier_id}_{direction}_hysteresis.png`：桥墩滞回曲线
- `comprehensive_hysteresis.png`：综合滞回曲线图

### 滞回特性参数
- 最大位移/力
- 初始刚度
- 能量耗散
- 等效阻尼比

## 工程意义

### 支座滞回曲线
1. **刚度评估**：初始刚度反映支座的弹性特性
2. **耗能能力**：滞回环面积反映支座的耗能能力
3. **阻尼特性**：等效阻尼比用于结构减震设计
4. **极限状态**：最大位移用于支座极限状态验算

### 桥墩滞回曲线
1. **塑性发展**：弯矩-曲率关系反映塑性铰的发展
2. **延性评估**：曲率延性系数评估桥墩的变形能力
3. **能量耗散**：累积耗散能量反映桥墩的耗能贡献
4. **损伤评估**：滞回特性用于结构损伤评估

## 注意事项

1. **数据完整性**：确保地震时程分析已完成并生成结果文件
2. **单位一致性**：模块自动处理单位转换，输出采用工程常用单位
3. **文件路径**：确保结果文件路径正确
4. **支座参数**：支座力计算需要正确的支座参数设置

## 故障排除

### 常见问题

1. **未找到数据文件**
   - 检查results目录是否存在
   - 确认地震时程分析已完成

2. **支座力计算异常**
   - 检查支座参数设置
   - 确认轴向荷载数据完整

3. **桥墩数据缺失**
   - 检查recorder设置
   - 确认桥墩ID正确

### 调试建议

```python
# 检查数据文件
import os
print(os.listdir('results'))

# 检查支座数据
plotter = HysteresisPlotter('results')
bearing_data = plotter.load_bearing_data()
print(f"时间步数: {len(bearing_data)}")

# 检查桥墩数据
pier_data = plotter.load_pier_data('pier_x15.0_y0.0')
print(f"数据项: {list(pier_data.keys())}")
```

## 扩展功能

模块设计为模块化结构，可以方便地扩展：

1. **新的支座类型**：在`BearingForceCalculator`中添加新的本构关系
2. **新的分析指标**：在滞回特性计算中添加新的参数
3. **新的绘图样式**：自定义matplotlib绘图参数
4. **批量处理**：处理多个地震动的滞回响应

## 参考文献

1. 公路桥梁抗震设计细则 (JTG/T 2231-01-2020)
2. 建筑抗震设计规范 (GB 50011-2010)
3. OpenSees用户手册
4. 结构动力学理论与应用
