"""
地震时程分析滞回曲线绘制模块

该模块提供专业的滞回曲线绘制功能，用于分析地震时程分析过程中：
1. 支座的力-位移滞回曲线
2. 桥墩底部节点的弯矩-曲率滞回曲线

作者: 桥梁工程师
日期: 2025-08-16
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.gridspec import GridSpec
import os
import csv
from typing import Dict, List, Tuple, Optional
from analysis.utils.data_readers import read_bearing_displacements
from utils.bearing_force_calculator import (
    BearingForceCalculator,
    create_bearing_calculator_from_model,
    calculate_bearing_hysteresis_properties
)


class HysteresisPlotter:
    """滞回曲线绘制器"""
    
    def __init__(self, results_dir: str = 'results'):
        """
        初始化滞回曲线绘制器
        
        参数:
            results_dir: 结果文件目录
        """
        self.results_dir = results_dir
        self.bearing_data = {}
        self.pier_data = {}
        self.use_english = False  # 标记是否使用英文标签
        
        # 设置中文字体 - 修复中文显示问题
        self._setup_chinese_font()

        # 设置绘图样式
        try:
            plt.style.use('seaborn-v0_8-whitegrid')
        except:
            plt.style.use('default')

    def _setup_chinese_font(self):
        """设置中文字体显示"""
        import matplotlib.font_manager as fm

        fonts = ['Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi']

        # 查找可用的中文字体
        available_fonts = [f.name for f in fm.fontManager.ttflist]
        chinese_font = None

        for font in fonts:
            if font in available_fonts:
                chinese_font = font
                break

        # 设置字体
        # plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        self.use_english = True

        plt.rcParams['axes.unicode_minus'] = False

    def _get_labels(self, key):
        """获取标签文本（中文或英文）"""
        chinese_labels = {
            'bearing_title': '支座 #{} 力-位移滞回曲线',
            'bearing_comprehensive_title': '支座 #{} 地震响应分析 ({}模型)',
            'pier_title': '桥墩 {} {}方向 弯矩-曲率滞回曲线',
            'pier_comprehensive_title': '桥墩 {} {}方向 地震响应分析',
            'comprehensive_title': '地震时程分析滞回响应综合图',
            'displacement': '相对位移 (mm)',
            'force': '恢复力 (kN)',
            'curvature': '曲率 (1/km)',
            'moment': '弯矩 (MN·m)',
            'time': '时间 (s)',
            'displacement_history': '位移时程',
            'force_history': '恢复力时程',
            'curvature_history': '曲率时程',
            'moment_history': '弯矩时程',
            'energy_dissipation': '累积耗散能量 (MN·m)',
            'energy_title': '桥墩能量耗散时程',
            'hysteresis_properties': '''滞回特性参数:
            最大位移: {:.2f} mm
            最大恢复力: {:.1f} kN
            初始刚度: {:.1f} MN/m
            能量耗散: {:.1f} kN·mm
            等效阻尼比: {:.3f}'''
        }

        english_labels = {
            'bearing_title': 'Bearing #{} Force-Displacement Hysteresis',
            'bearing_comprehensive_title': 'Bearing #{} Seismic Response Analysis ({} Model)',
            'pier_title': 'Pier {} {}-Direction Moment-Curvature Hysteresis',
            'pier_comprehensive_title': 'Pier {} {}-Direction Seismic Response',
            'comprehensive_title': 'Comprehensive Hysteresis Response Analysis',
            'displacement': 'Relative Displacement (mm)',
            'force': 'Restoring Force (kN)',
            'curvature': 'Curvature (1/km)',
            'moment': 'Moment (MN·m)',
            'time': 'Time (s)',
            'displacement_history': 'Displacement History',
            'force_history': 'Force History',
            'curvature_history': 'Curvature History',
            'moment_history': 'Moment History',
            'energy_dissipation': 'Cumulative Energy Dissipation (MN·m)',
            'energy_title': 'Pier Energy Dissipation History',
            'hysteresis_properties': '''Hysteresis Properties:
Max Displacement: {:.2f} mm
Max Force: {:.1f} kN
Initial Stiffness: {:.1f} MN/m
Energy Dissipation: {:.1f} kN·mm
Equivalent Damping: {:.3f}'''
        }

        if self.use_english:
            return english_labels.get(key, key)
        else:
            return chinese_labels.get(key, key)
        
    def load_bearing_data(self, bearing_file: str = None, model=None) -> Dict:
        """
        加载支座相对位移数据
        
        参数:
            bearing_file: 支座相对位移数据文件路径
            model: 桥梁模型对象
            
        返回:
            dict: 支座数据字典
        """
        if bearing_file is None:
            bearing_file = os.path.join(self.results_dir, 'bearing_relative_disps.csv')
            
        if not os.path.exists(bearing_file):
            print(f"警告: 支座数据文件 {bearing_file} 不存在")
            return {}
            
        self.bearing_data = read_bearing_displacements(bearing_file, model)
        print(f"已加载支座数据，包含 {len(self.bearing_data)} 个时间步")
        return self.bearing_data
        
    def load_pier_data(self, pier_id: str) -> Dict:
        """
        加载桥墩响应数据
        
        参数:
            pier_id: 桥墩标识符，如 'pier_x15.0_y0.0'
            
        返回:
            dict: 桥墩数据字典，包含位移、弯矩、曲率数据
        """
        pier_data = {}
        
        # 构建文件路径
        disp_file = os.path.join(self.results_dir, f'pier_disp_{pier_id}.txt')
        moment_file = os.path.join(self.results_dir, f'pier_moment_{pier_id}.txt')
        curvature_file = os.path.join(self.results_dir, f'pier_curvature_{pier_id}.txt')
        
        # 读取位移数据
        if os.path.exists(disp_file):
            try:
                disp_data = np.loadtxt(disp_file)
                if disp_data.size > 0:
                    if disp_data.ndim == 1:
                        disp_data = disp_data.reshape(1, -1)
                    pier_data['time'] = disp_data[:, 0]
                    pier_data['disp_x'] = disp_data[:, 1]  # X方向位移 (m)
                    pier_data['disp_y'] = disp_data[:, 2]  # Y方向位移 (m)
                    pier_data['disp_z'] = disp_data[:, 3]  # Z方向位移 (m)
            except Exception as e:
                print(f"读取位移文件 {disp_file} 时出错: {e}")
                
        # 读取弯矩数据
        if os.path.exists(moment_file):
            try:
                moment_data = np.loadtxt(moment_file)
                if moment_data.size > 0:
                    if moment_data.ndim == 1:
                        moment_data = moment_data.reshape(1, -1)

                    print(f"弯矩数据形状: {moment_data.shape}")

                    if moment_data.shape[1] == 13:
                        # 13列格式: time + 6列第一单元 + 6列第二单元
                        # 使用第一个单元的数据 (底部单元)
                        print("检测到13列弯矩数据格式，使用第一个单元数据")
                        pier_data['moment_x'] = moment_data[:, 4]  # 第一单元X方向弯矩
                        pier_data['moment_y'] = moment_data[:, 5]  # 第一单元Y方向弯矩
                        pier_data['shear_x'] = moment_data[:, 1]   # 第一单元X方向剪力
                        pier_data['shear_y'] = moment_data[:, 2]   # 第一单元Y方向剪力
                        pier_data['axial'] = moment_data[:, 3]     # 第一单元轴力
                    elif moment_data.shape[1] == 7:
                        # 7列格式: time + 6列内力
                        print("检测到7列弯矩数据格式")
                        pier_data['moment_x'] = moment_data[:, 4]  # X方向弯矩
                        pier_data['moment_y'] = moment_data[:, 5]  # Y方向弯矩
                        pier_data['shear_x'] = moment_data[:, 1]   # X方向剪力
                        pier_data['shear_y'] = moment_data[:, 2]   # Y方向剪力
                        pier_data['axial'] = moment_data[:, 3]     # 轴力
                    else:
                        print(f"未知的弯矩数据格式，列数: {moment_data.shape[1]}")
                        print("前3行数据:")
                        print(moment_data[:3])

                    # 打印弯矩数据范围用于诊断
                    if 'moment_x' in pier_data:
                        print(f"X方向弯矩范围: {pier_data['moment_x'].min():.2e} - {pier_data['moment_x'].max():.2e} N·m")
                        print(f"Y方向弯矩范围: {pier_data['moment_y'].min():.2e} - {pier_data['moment_y'].max():.2e} N·m")

            except Exception as e:
                print(f"读取弯矩文件 {moment_file} 时出错: {e}")
                
        # 读取曲率数据
        if os.path.exists(curvature_file):
            try:
                curvature_data = np.loadtxt(curvature_file)
                if curvature_data.size > 0:
                    if curvature_data.ndim == 1:
                        curvature_data = curvature_data.reshape(1, -1)
                    # 截面变形: [轴向应变, 曲率_x, 曲率_y]
                    pier_data['curvature_x'] = curvature_data[:, 1]  # X方向曲率 (1/m)
                    pier_data['curvature_y'] = curvature_data[:, 2]  # Y方向曲率 (1/m)
            except Exception as e:
                print(f"读取曲率文件 {curvature_file} 时出错: {e}")
                
        self.pier_data[pier_id] = pier_data
        print(f"已加载桥墩 {pier_id} 数据，包含 {len(pier_data.get('time', []))} 个时间步")
        return pier_data
        
    def calculate_bearing_force(self, bearing_idx: int, model=None,
                                bearing_type: str = 'auto') -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        计算支座恢复力（专业计算）

        参数:
            bearing_idx: 支座索引
            model: 桥梁模型对象
            bearing_type: 支座类型 ('auto', 'rubber', 'friction', 'bilinear')

        返回:
            tuple: (时间数组, 位移数组, 力数组)
        """
        if not self.bearing_data:
            print("警告: 未加载支座数据")
            return np.array([]), np.array([]), np.array([])

        # 提取指定支座的位移时程
        times = []
        displacements = []
        elem_tag = None

        for time in sorted(self.bearing_data.keys()):
            for bearing in self.bearing_data[time]:
                if bearing['bearing_idx'] == bearing_idx:
                    times.append(time)
                    # 使用水平位移合成 (mm -> m)
                    disp_h = np.sqrt(bearing['rel_disp_x']**2 + bearing['rel_disp_y']**2) / 1000
                    displacements.append(disp_h)
                    # 获取支座元素标签
                    if elem_tag is None:
                        elem_tag = bearing.get('elem_tag')
                    break

        times = np.array(times)
        displacements = np.array(displacements)

        if len(times) == 0:
            return times, displacements, np.array([])

        # 使用专业的支座力计算器
        if model:
            calculator = create_bearing_calculator_from_model(model)

            # 自动判断支座类型
            if bearing_type == 'auto':
                if model.params.bearing.get('friction', False):
                    bearing_type = 'friction'
                else:
                    bearing_type = 'bilinear'

            # 计算恢复力时程
            forces = calculator.calculate_force_time_history(
                displacements, times, elem_tag, bearing_type
            )
        else:
            # 简化线性计算
            k_bearing = 1e8  # 默认水平刚度 N/m
            forces = k_bearing * displacements

        return times, displacements, forces
        
    def plot_bearing_hysteresis(self, bearing_idx: int = 0, model=None,
                                bearing_type: str = 'auto', save_path: str = None,
                                show_plot: bool = True) -> None:
        """
        绘制支座滞回曲线（专业版）

        参数:
            bearing_idx: 支座索引（默认第一个支座）
            model: 桥梁模型对象
            bearing_type: 支座类型 ('auto', 'rubber', 'friction', 'bilinear')
            save_path: 保存路径
            show_plot: 是否显示图形
        """
        if not self.bearing_data:
            print("错误: 未加载支座数据，请先调用 load_bearing_data()")
            return

        # 计算支座力和位移
        times, displacements, forces = self.calculate_bearing_force(bearing_idx, model, bearing_type)

        if len(times) == 0:
            print(f"错误: 未找到支座 {bearing_idx} 的数据")
            return

        # 计算滞回特性参数
        hysteresis_props = calculate_bearing_hysteresis_properties(displacements, forces)

        # 创建图形
        fig = plt.figure(figsize=(14, 10))
        gs = GridSpec(2, 2, figure=fig, hspace=0.3, wspace=0.3)

        # 主滞回曲线
        ax1 = fig.add_subplot(gs[0, :])
        ax1.plot(displacements * 1000, forces / 1000, 'b-', linewidth=2, alpha=0.8)
        ax1.set_xlabel(self._get_labels('displacement'), fontsize=12)
        ax1.set_ylabel(self._get_labels('force'), fontsize=12)
        ax1.set_title(self._get_labels('bearing_title').format(bearing_idx), fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)

        # 添加特性参数文本
        props_text = self._get_labels('hysteresis_properties').format(
            hysteresis_props['max_displacement']*1000,
            hysteresis_props['max_force']/1000,
            hysteresis_props['initial_stiffness']/1e6,
            hysteresis_props['energy_dissipated']/1000,
            hysteresis_props['equivalent_damping_ratio']
        )

        ax1.text(0.02, 0.98, props_text, transform=ax1.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        # 位移时程
        ax2 = fig.add_subplot(gs[1, 0])
        ax2.plot(times, displacements * 1000, 'r-', linewidth=1.5)
        ax2.set_xlabel(self._get_labels('time'), fontsize=12)
        ax2.set_ylabel(self._get_labels('displacement'), fontsize=12, color='r')
        ax2.set_title(self._get_labels('displacement_history'), fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='y', labelcolor='r')

        # 恢复力时程
        ax3 = fig.add_subplot(gs[1, 1])
        ax3.plot(times, forces / 1000, 'b-', linewidth=1.5)
        ax3.set_xlabel(self._get_labels('time'), fontsize=12)
        ax3.set_ylabel(self._get_labels('force'), fontsize=12, color='b')
        ax3.set_title(self._get_labels('force_history'), fontsize=12)
        ax3.grid(True, alpha=0.3)
        ax3.tick_params(axis='y', labelcolor='b')

        plt.suptitle(self._get_labels('bearing_comprehensive_title').format(bearing_idx, bearing_type.upper()),
                    fontsize=16, fontweight='bold')

        # 保存图形
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"支座滞回曲线已保存至: {save_path}")

        if show_plot:
            plt.show()
        else:
            plt.close()
            
    def plot_pier_hysteresis(self, pier_id: str, direction: str = 'x', 
                         save_path: str = None, show_plot: bool = True) -> None:
        """
        绘制桥墩底部弯矩-曲率滞回曲线
        
        参数:
            pier_id: 桥墩标识符
            direction: 方向 ('x' 或 'y')
            save_path: 保存路径
            show_plot: 是否显示图形
        """
        if pier_id not in self.pier_data:
            print(f"错误: 未加载桥墩 {pier_id} 数据，请先调用 load_pier_data()")
            return
            
        pier_data = self.pier_data[pier_id]
        
        # 检查数据完整性
        moment_key = f'moment_{direction}'
        curvature_key = f'curvature_{direction}'
        
        if moment_key not in pier_data or curvature_key not in pier_data:
            print(f"错误: 桥墩 {pier_id} 缺少 {direction} 方向的弯矩或曲率数据")
            return
            
        moments = pier_data[moment_key] / 1e6  # N·m -> MN·m
        curvatures = pier_data[curvature_key] * 1000  # 1/m -> 1/km (便于显示)
        times = pier_data.get('time', np.arange(len(moments)))
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
        
        # 绘制弯矩-曲率滞回曲线
        ax1.plot(curvatures, moments, 'b-', linewidth=1.5, alpha=0.8)
        ax1.set_xlabel(self._get_labels('curvature'))
        ax1.set_ylabel(self._get_labels('moment'))
        ax1.set_title(self._get_labels('pier_title').format(pier_id, direction.upper()))
        ax1.grid(True, alpha=0.3)

        # 绘制时程曲线
        ax2.plot(times, curvatures, 'r-', linewidth=1.0)
        ax2_twin = ax2.twinx()
        ax2_twin.plot(times, moments, 'b-', linewidth=1.0)

        ax2.set_xlabel(self._get_labels('time'))
        ax2.set_ylabel(self._get_labels('curvature'), color='r')
        ax2_twin.set_ylabel(self._get_labels('moment'), color='b')
        ax2.set_title(self._get_labels('pier_comprehensive_title').format(pier_id, direction.upper()))
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图形
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"桥墩滞回曲线已保存至: {save_path}")
            
        if show_plot:
            plt.show()
        else:
            plt.close()

    def plot_comprehensive_hysteresis(self, bearing_idx: int = 0, pier_id: str = None,
                                    model=None, save_dir: str = None) -> None:
        """
        绘制综合滞回曲线图（支座和桥墩）

        参数:
            bearing_idx: 支座索引
            pier_id: 桥墩标识符
            model: 桥梁模型对象
            save_dir: 保存目录
        """
        if save_dir is None:
            save_dir = self.results_dir

        os.makedirs(save_dir, exist_ok=True)

        # 创建综合图形
        fig = plt.figure(figsize=(16, 10))
        gs = GridSpec(2, 2, figure=fig, hspace=0.3, wspace=0.3)

        # 支座滞回曲线
        if self.bearing_data:
            ax1 = fig.add_subplot(gs[0, 0])
            times, displacements, forces = self.calculate_bearing_force(bearing_idx, model)

            if len(times) > 0:
                ax1.plot(displacements * 1000, forces / 1000, 'b-', linewidth=1.5, alpha=0.8)
                ax1.set_xlabel(self._get_labels('displacement'))
                ax1.set_ylabel(self._get_labels('force'))
                ax1.set_title(self._get_labels('bearing_title').format(bearing_idx))
                ax1.grid(True, alpha=0.3)

        # 桥墩弯矩-曲率滞回曲线
        if pier_id and pier_id in self.pier_data:
            pier_data = self.pier_data[pier_id]

            # X方向
            if 'moment_x' in pier_data and 'curvature_x' in pier_data:
                ax2 = fig.add_subplot(gs[0, 1])
                moments_x = pier_data['moment_x'] / 1e6
                curvatures_x = pier_data['curvature_x'] * 1000
                ax2.plot(curvatures_x, moments_x, 'r-', linewidth=1.5, alpha=0.8)
                ax2.set_xlabel(self._get_labels('curvature'))
                ax2.set_ylabel(self._get_labels('moment'))
                ax2.set_title(self._get_labels('pier_title').format(pier_id, 'X'))
                ax2.grid(True, alpha=0.3)

            # Y方向
            if 'moment_y' in pier_data and 'curvature_y' in pier_data:
                ax3 = fig.add_subplot(gs[1, 0])
                moments_y = pier_data['moment_y'] / 1e6
                curvatures_y = pier_data['curvature_y'] * 1000
                ax3.plot(curvatures_y, moments_y, 'g-', linewidth=1.5, alpha=0.8)
                ax3.set_xlabel(self._get_labels('curvature'))
                ax3.set_ylabel(self._get_labels('moment'))
                ax3.set_title(self._get_labels('pier_title').format(pier_id, 'Y'))
                ax3.grid(True, alpha=0.3)

        # 能量耗散分析
        ax4 = fig.add_subplot(gs[1, 1])
        if pier_id and pier_id in self.pier_data:
            pier_data = self.pier_data[pier_id]
            times = pier_data.get('time', [])

            if 'moment_y' in pier_data and 'curvature_y' in pier_data and len(times) > 1:
                # 计算累积耗散能量（简化）
                moments_y = pier_data['moment_y'] / 1e6
                curvatures_y = pier_data['curvature_y']

                # 数值积分计算能量耗散
                energy = np.zeros(len(times))
                for i in range(1, len(times)):
                    dt = times[i] - times[i-1]
                    dE = abs(moments_x[i] * (curvatures_y[i] - curvatures_y[i-1]))
                    energy[i] = energy[i-1] + dE

                ax4.plot(times, energy, 'purple', linewidth=2)
                ax4.set_xlabel(self._get_labels('time'))
                ax4.set_ylabel(self._get_labels('energy_dissipation'))
                ax4.set_title(self._get_labels('energy_title'))
                ax4.grid(True, alpha=0.3)

        plt.suptitle(self._get_labels('comprehensive_title'), fontsize=16, fontweight='bold')

        # 保存图形
        save_path = os.path.join(save_dir, 'comprehensive_hysteresis.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"综合滞回曲线图已保存至: {save_path}")
        plt.show()


def plot_seismic_hysteresis(results_dir: str = 'results', bearing_idx: int = 0,
                            pier_id: str = None, model=None) -> None:
    """
    便捷函数：绘制地震时程分析滞回曲线

    参数:
        results_dir: 结果文件目录
        bearing_idx: 支座索引（默认第一个支座）
        pier_id: 桥墩标识符（如 'pier_x15.0_y0.0'）
        model: 桥梁模型对象

    使用示例:
        # 绘制第一个支座和指定桥墩的滞回曲线
        plot_seismic_hysteresis(
            results_dir='results',
            bearing_idx=0,
            pier_id='pier_x15.0_y0.0',
            model=bridge_model
        )
    """
    # 创建绘图器
    plotter = HysteresisPlotter(results_dir)

    # 加载数据
    plotter.load_bearing_data(model=model)

    if pier_id:
        plotter.load_pier_data(pier_id)

    # 绘制综合滞回曲线
    plotter.plot_comprehensive_hysteresis(
        bearing_idx=bearing_idx,
        pier_id=pier_id,
        model=model,
        save_dir=results_dir
    )

    # 单独绘制支座滞回曲线
    save_path = os.path.join(results_dir, f'bearing_{bearing_idx}_hysteresis.png')
    plotter.plot_bearing_hysteresis(
        bearing_idx=bearing_idx,
        model=model,
        save_path=save_path,
        show_plot=False
    )

    # 单独绘制桥墩滞回曲线
    if pier_id:
        for direction in ['x', 'y']:
            save_path = os.path.join(results_dir, f'pier_{pier_id}_{direction}_hysteresis.png')
            plotter.plot_pier_hysteresis(
                pier_id=pier_id,
                direction=direction,
                save_path=save_path,
                show_plot=False
            )
