"""
地震时程分析滞回曲线绘制模块

该模块提供专业的滞回曲线绘制功能，用于分析地震时程分析过程中：
1. 支座的力-位移滞回曲线
2. 桥墩底部节点的弯矩-曲率滞回曲线

作者: 桥梁工程师
日期: 2025-08-16
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.gridspec import GridSpec
import os
import csv
from typing import Dict, List, Tuple, Optional
from analysis.utils.data_readers import read_bearing_displacements
from utils.bearing_force_calculator import (
    BearingForceCalculator,
    create_bearing_calculator_from_model,
    calculate_bearing_hysteresis_properties
)


class HysteresisPlotter:
    """滞回曲线绘制器"""
    
    def __init__(self, results_dir: str = 'results'):
        """
        初始化滞回曲线绘制器
        
        参数:
            results_dir: 结果文件目录
        """
        self.results_dir = results_dir
        self.bearing_data = {}
        self.pier_data = {}
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 设置绘图样式
        plt.style.use('seaborn-v0_8-whitegrid')
        
    def load_bearing_data(self, bearing_file: str = None, model=None) -> Dict:
        """
        加载支座相对位移数据
        
        参数:
            bearing_file: 支座相对位移数据文件路径
            model: 桥梁模型对象
            
        返回:
            dict: 支座数据字典
        """
        if bearing_file is None:
            bearing_file = os.path.join(self.results_dir, 'bearing_relative_disps.csv')
            
        if not os.path.exists(bearing_file):
            print(f"警告: 支座数据文件 {bearing_file} 不存在")
            return {}
            
        self.bearing_data = read_bearing_displacements(bearing_file, model)
        print(f"已加载支座数据，包含 {len(self.bearing_data)} 个时间步")
        return self.bearing_data
        
    def load_pier_data(self, pier_id: str) -> Dict:
        """
        加载桥墩响应数据
        
        参数:
            pier_id: 桥墩标识符，如 'pier_x15.0_y0.0'
            
        返回:
            dict: 桥墩数据字典，包含位移、弯矩、曲率数据
        """
        pier_data = {}
        
        # 构建文件路径
        disp_file = os.path.join(self.results_dir, f'pier_disp_{pier_id}.txt')
        moment_file = os.path.join(self.results_dir, f'pier_moment_{pier_id}.txt')
        curvature_file = os.path.join(self.results_dir, f'pier_curvature_{pier_id}.txt')
        
        # 读取位移数据
        if os.path.exists(disp_file):
            try:
                disp_data = np.loadtxt(disp_file)
                if disp_data.size > 0:
                    if disp_data.ndim == 1:
                        disp_data = disp_data.reshape(1, -1)
                    pier_data['time'] = disp_data[:, 0]
                    pier_data['disp_x'] = disp_data[:, 1]  # X方向位移 (m)
                    pier_data['disp_y'] = disp_data[:, 2]  # Y方向位移 (m)
                    pier_data['disp_z'] = disp_data[:, 3]  # Z方向位移 (m)
            except Exception as e:
                print(f"读取位移文件 {disp_file} 时出错: {e}")
                
        # 读取弯矩数据
        if os.path.exists(moment_file):
            try:
                moment_data = np.loadtxt(moment_file)
                if moment_data.size > 0:
                    if moment_data.ndim == 1:
                        moment_data = moment_data.reshape(1, -1)
                    # forceBeamColumn单元的内力: [Vx, Vy, P, Mx, My, T]
                    pier_data['moment_x'] = moment_data[:, 4]  # X方向弯矩 (N·m)
                    pier_data['moment_y'] = moment_data[:, 5]  # Y方向弯矩 (N·m)
                    pier_data['shear_x'] = moment_data[:, 1]   # X方向剪力 (N)
                    pier_data['shear_y'] = moment_data[:, 2]   # Y方向剪力 (N)
                    pier_data['axial'] = moment_data[:, 3]     # 轴力 (N)
            except Exception as e:
                print(f"读取弯矩文件 {moment_file} 时出错: {e}")
                
        # 读取曲率数据
        if os.path.exists(curvature_file):
            try:
                curvature_data = np.loadtxt(curvature_file)
                if curvature_data.size > 0:
                    if curvature_data.ndim == 1:
                        curvature_data = curvature_data.reshape(1, -1)
                    # 截面变形: [轴向应变, 曲率_x, 曲率_y]
                    pier_data['curvature_x'] = curvature_data[:, 1]  # X方向曲率 (1/m)
                    pier_data['curvature_y'] = curvature_data[:, 2]  # Y方向曲率 (1/m)
            except Exception as e:
                print(f"读取曲率文件 {curvature_file} 时出错: {e}")
                
        self.pier_data[pier_id] = pier_data
        print(f"已加载桥墩 {pier_id} 数据，包含 {len(pier_data.get('time', []))} 个时间步")
        return pier_data
        
    def calculate_bearing_force(self, bearing_idx: int, model=None,
                              bearing_type: str = 'auto') -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        计算支座恢复力（专业计算）

        参数:
            bearing_idx: 支座索引
            model: 桥梁模型对象
            bearing_type: 支座类型 ('auto', 'rubber', 'friction', 'bilinear')

        返回:
            tuple: (时间数组, 位移数组, 力数组)
        """
        if not self.bearing_data:
            print("警告: 未加载支座数据")
            return np.array([]), np.array([]), np.array([])

        # 提取指定支座的位移时程
        times = []
        displacements = []
        elem_tag = None

        for time in sorted(self.bearing_data.keys()):
            for bearing in self.bearing_data[time]:
                if bearing['bearing_idx'] == bearing_idx:
                    times.append(time)
                    # 使用水平位移合成 (mm -> m)
                    disp_h = np.sqrt(bearing['rel_disp_x']**2 + bearing['rel_disp_y']**2) / 1000
                    displacements.append(disp_h)
                    # 获取支座元素标签
                    if elem_tag is None:
                        elem_tag = bearing.get('elem_tag')
                    break

        times = np.array(times)
        displacements = np.array(displacements)

        if len(times) == 0:
            return times, displacements, np.array([])

        # 使用专业的支座力计算器
        if model:
            calculator = create_bearing_calculator_from_model(model)

            # 自动判断支座类型
            if bearing_type == 'auto':
                if model.params.bearing.get('friction', False):
                    bearing_type = 'friction'
                else:
                    bearing_type = 'bilinear'

            # 计算恢复力时程
            forces = calculator.calculate_force_time_history(
                displacements, times, elem_tag, bearing_type
            )
        else:
            # 简化线性计算
            k_bearing = 1e8  # 默认水平刚度 N/m
            forces = k_bearing * displacements

        return times, displacements, forces
        
    def plot_bearing_hysteresis(self, bearing_idx: int = 0, model=None,
                              bearing_type: str = 'auto', save_path: str = None,
                              show_plot: bool = True) -> None:
        """
        绘制支座滞回曲线（专业版）

        参数:
            bearing_idx: 支座索引（默认第一个支座）
            model: 桥梁模型对象
            bearing_type: 支座类型 ('auto', 'rubber', 'friction', 'bilinear')
            save_path: 保存路径
            show_plot: 是否显示图形
        """
        if not self.bearing_data:
            print("错误: 未加载支座数据，请先调用 load_bearing_data()")
            return

        # 计算支座力和位移
        times, displacements, forces = self.calculate_bearing_force(bearing_idx, model, bearing_type)

        if len(times) == 0:
            print(f"错误: 未找到支座 {bearing_idx} 的数据")
            return

        # 计算滞回特性参数
        hysteresis_props = calculate_bearing_hysteresis_properties(displacements, forces)

        # 创建图形
        fig = plt.figure(figsize=(14, 10))
        gs = GridSpec(2, 2, figure=fig, hspace=0.3, wspace=0.3)

        # 主滞回曲线
        ax1 = fig.add_subplot(gs[0, :])
        ax1.plot(displacements * 1000, forces / 1000, 'b-', linewidth=2, alpha=0.8)
        ax1.set_xlabel('相对位移 (mm)', fontsize=12)
        ax1.set_ylabel('恢复力 (kN)', fontsize=12)
        ax1.set_title(f'支座 #{bearing_idx} 力-位移滞回曲线', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)

        # 添加特性参数文本
        props_text = f"""滞回特性参数:
最大位移: {hysteresis_props['max_displacement']*1000:.2f} mm
最大恢复力: {hysteresis_props['max_force']/1000:.1f} kN
初始刚度: {hysteresis_props['initial_stiffness']/1e6:.1f} MN/m
能量耗散: {hysteresis_props['energy_dissipated']/1000:.1f} kN·mm
等效阻尼比: {hysteresis_props['equivalent_damping_ratio']:.3f}"""

        ax1.text(0.02, 0.98, props_text, transform=ax1.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        # 位移时程
        ax2 = fig.add_subplot(gs[1, 0])
        ax2.plot(times, displacements * 1000, 'r-', linewidth=1.5)
        ax2.set_xlabel('时间 (s)', fontsize=12)
        ax2.set_ylabel('相对位移 (mm)', fontsize=12, color='r')
        ax2.set_title('位移时程', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.tick_params(axis='y', labelcolor='r')

        # 恢复力时程
        ax3 = fig.add_subplot(gs[1, 1])
        ax3.plot(times, forces / 1000, 'b-', linewidth=1.5)
        ax3.set_xlabel('时间 (s)', fontsize=12)
        ax3.set_ylabel('恢复力 (kN)', fontsize=12, color='b')
        ax3.set_title('恢复力时程', fontsize=12)
        ax3.grid(True, alpha=0.3)
        ax3.tick_params(axis='y', labelcolor='b')

        plt.suptitle(f'支座 #{bearing_idx} 地震响应分析 ({bearing_type.upper()}模型)',
                    fontsize=16, fontweight='bold')

        # 保存图形
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"支座滞回曲线已保存至: {save_path}")

        if show_plot:
            plt.show()
        else:
            plt.close()
            
    def plot_pier_hysteresis(self, pier_id: str, direction: str = 'x', 
                           save_path: str = None, show_plot: bool = True) -> None:
        """
        绘制桥墩底部弯矩-曲率滞回曲线
        
        参数:
            pier_id: 桥墩标识符
            direction: 方向 ('x' 或 'y')
            save_path: 保存路径
            show_plot: 是否显示图形
        """
        if pier_id not in self.pier_data:
            print(f"错误: 未加载桥墩 {pier_id} 数据，请先调用 load_pier_data()")
            return
            
        pier_data = self.pier_data[pier_id]
        
        # 检查数据完整性
        moment_key = f'moment_{direction}'
        curvature_key = f'curvature_{direction}'
        
        if moment_key not in pier_data or curvature_key not in pier_data:
            print(f"错误: 桥墩 {pier_id} 缺少 {direction} 方向的弯矩或曲率数据")
            return
            
        moments = pier_data[moment_key] / 1e6  # N·m -> MN·m
        curvatures = pier_data[curvature_key] * 1000  # 1/m -> 1/km (便于显示)
        times = pier_data.get('time', np.arange(len(moments)))
        
        # 创建图形
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
        
        # 绘制弯矩-曲率滞回曲线
        ax1.plot(curvatures, moments, 'b-', linewidth=1.5, alpha=0.8)
        ax1.set_xlabel('曲率 (1/km)')
        ax1.set_ylabel('弯矩 (MN·m)')
        ax1.set_title(f'桥墩 {pier_id} {direction.upper()}方向 弯矩-曲率滞回曲线')
        ax1.grid(True, alpha=0.3)
        
        # 绘制时程曲线
        ax2.plot(times, curvatures, 'r-', linewidth=1.0, label='曲率')
        ax2_twin = ax2.twinx()
        ax2_twin.plot(times, moments, 'b-', linewidth=1.0, label='弯矩')
        
        ax2.set_xlabel('时间 (s)')
        ax2.set_ylabel('曲率 (1/km)', color='r')
        ax2_twin.set_ylabel('弯矩 (MN·m)', color='b')
        ax2.set_title(f'桥墩 {pier_id} {direction.upper()}方向 时程响应')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图形
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"桥墩滞回曲线已保存至: {save_path}")
            
        if show_plot:
            plt.show()
        else:
            plt.close()

    def plot_comprehensive_hysteresis(self, bearing_idx: int = 0, pier_id: str = None,
                                    model=None, save_dir: str = None) -> None:
        """
        绘制综合滞回曲线图（支座和桥墩）

        参数:
            bearing_idx: 支座索引
            pier_id: 桥墩标识符
            model: 桥梁模型对象
            save_dir: 保存目录
        """
        if save_dir is None:
            save_dir = self.results_dir

        os.makedirs(save_dir, exist_ok=True)

        # 创建综合图形
        fig = plt.figure(figsize=(16, 10))
        gs = GridSpec(2, 2, figure=fig, hspace=0.3, wspace=0.3)

        # 支座滞回曲线
        if self.bearing_data:
            ax1 = fig.add_subplot(gs[0, 0])
            times, displacements, forces = self.calculate_bearing_force(bearing_idx, model)

            if len(times) > 0:
                ax1.plot(displacements * 1000, forces / 1000, 'b-', linewidth=1.5, alpha=0.8)
                ax1.set_xlabel('相对位移 (mm)')
                ax1.set_ylabel('恢复力 (kN)')
                ax1.set_title(f'支座 #{bearing_idx} 滞回曲线')
                ax1.grid(True, alpha=0.3)

        # 桥墩弯矩-曲率滞回曲线
        if pier_id and pier_id in self.pier_data:
            pier_data = self.pier_data[pier_id]

            # X方向
            if 'moment_x' in pier_data and 'curvature_x' in pier_data:
                ax2 = fig.add_subplot(gs[0, 1])
                moments_x = pier_data['moment_x'] / 1e6
                curvatures_x = pier_data['curvature_x'] * 1000
                ax2.plot(curvatures_x, moments_x, 'r-', linewidth=1.5, alpha=0.8)
                ax2.set_xlabel('曲率 (1/km)')
                ax2.set_ylabel('弯矩 (MN·m)')
                ax2.set_title(f'桥墩 {pier_id} X方向 M-φ滞回曲线')
                ax2.grid(True, alpha=0.3)

            # Y方向
            if 'moment_y' in pier_data and 'curvature_y' in pier_data:
                ax3 = fig.add_subplot(gs[1, 0])
                moments_y = pier_data['moment_y'] / 1e6
                curvatures_y = pier_data['curvature_y'] * 1000
                ax3.plot(curvatures_y, moments_y, 'g-', linewidth=1.5, alpha=0.8)
                ax3.set_xlabel('曲率 (1/km)')
                ax3.set_ylabel('弯矩 (MN·m)')
                ax3.set_title(f'桥墩 {pier_id} Y方向 M-φ滞回曲线')
                ax3.grid(True, alpha=0.3)

        # 能量耗散分析
        ax4 = fig.add_subplot(gs[1, 1])
        if pier_id and pier_id in self.pier_data:
            pier_data = self.pier_data[pier_id]
            times = pier_data.get('time', [])

            if 'moment_x' in pier_data and 'curvature_x' in pier_data and len(times) > 1:
                # 计算累积耗散能量（简化）
                moments_x = pier_data['moment_x'] / 1e6
                curvatures_x = pier_data['curvature_x']

                # 数值积分计算能量耗散
                energy = np.zeros(len(times))
                for i in range(1, len(times)):
                    dt = times[i] - times[i-1]
                    dE = abs(moments_x[i] * (curvatures_x[i] - curvatures_x[i-1]))
                    energy[i] = energy[i-1] + dE

                ax4.plot(times, energy, 'purple', linewidth=2)
                ax4.set_xlabel('时间 (s)')
                ax4.set_ylabel('累积耗散能量 (MN·m)')
                ax4.set_title('桥墩能量耗散时程')
                ax4.grid(True, alpha=0.3)

        plt.suptitle('地震时程分析滞回响应综合图', fontsize=16, fontweight='bold')

        # 保存图形
        save_path = os.path.join(save_dir, 'comprehensive_hysteresis.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"综合滞回曲线图已保存至: {save_path}")
        plt.show()


def plot_seismic_hysteresis(results_dir: str = 'results', bearing_idx: int = 0,
                          pier_id: str = None, model=None) -> None:
    """
    便捷函数：绘制地震时程分析滞回曲线

    参数:
        results_dir: 结果文件目录
        bearing_idx: 支座索引（默认第一个支座）
        pier_id: 桥墩标识符（如 'pier_x15.0_y0.0'）
        model: 桥梁模型对象

    使用示例:
        # 绘制第一个支座和指定桥墩的滞回曲线
        plot_seismic_hysteresis(
            results_dir='results',
            bearing_idx=0,
            pier_id='pier_x15.0_y0.0',
            model=bridge_model
        )
    """
    # 创建绘图器
    plotter = HysteresisPlotter(results_dir)

    # 加载数据
    plotter.load_bearing_data(model=model)

    if pier_id:
        plotter.load_pier_data(pier_id)

    # 绘制综合滞回曲线
    plotter.plot_comprehensive_hysteresis(
        bearing_idx=bearing_idx,
        pier_id=pier_id,
        model=model,
        save_dir=results_dir
    )

    # 单独绘制支座滞回曲线
    save_path = os.path.join(results_dir, f'bearing_{bearing_idx}_hysteresis.png')
    plotter.plot_bearing_hysteresis(
        bearing_idx=bearing_idx,
        model=model,
        save_path=save_path,
        show_plot=False
    )

    # 单独绘制桥墩滞回曲线
    if pier_id:
        for direction in ['x', 'y']:
            save_path = os.path.join(results_dir, f'pier_{pier_id}_{direction}_hysteresis.png')
            plotter.plot_pier_hysteresis(
                pier_id=pier_id,
                direction=direction,
                save_path=save_path,
                show_plot=False
            )
