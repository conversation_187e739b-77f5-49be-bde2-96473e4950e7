# analysis/utils/data_readers.py

import csv

def read_bearing_displacements(file_path, model=None):
    """读取支座相对位移数据

    参数:
        file_path: 支座相对位移数据文件路径
        model: 桥梁模型对象，用于获取支座元素标签

    返回:
        dict: 支座相对位移数据字典 {time: [bearing_data, ...]}
    """
    bearing_data = {}

    # 如果提供了模型对象，创建支座索引到元素标签的映射
    bearing_idx_to_elem_tag = {}
    if model and hasattr(model, 'bearings') and 'elements' in model.bearings:
        for i, elem_tag in enumerate(model.bearings['elements']):
            bearing_idx_to_elem_tag[i] = elem_tag

    try:
        # 尝试不同的编码方式
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
        file_opened = False

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    reader = csv.reader(f)
                    # 跳过表头（可能有乱码）
                    header = next(reader)
                    print(f"使用编码 {encoding} 成功打开文件")
                    file_opened = True

                    row_count = 0
                    for row in reader:
                        if len(row) < 8:
                            continue  # 跳过不完整的行

                        try:
                            time = float(row[0])
                            span = int(row[1])
                            bearing_idx = int(row[2])
                            x_coord = float(row[3])
                            y_coord = float(row[4])
                            rel_disp_x = float(row[5])  # mm
                            rel_disp_y = float(row[6])  # mm
                            rel_disp_z = float(row[7])  # mm

                            if time not in bearing_data:
                                bearing_data[time] = []

                            # 创建支座数据字典
                            bearing_info = {
                                'span': span,
                                'bearing_idx': bearing_idx,
                                'x_coord': x_coord,
                                'y_coord': y_coord,
                                'rel_disp_x': rel_disp_x,
                                'rel_disp_y': rel_disp_y,
                                'rel_disp_z': rel_disp_z
                            }

                            # 如果有元素标签映射，添加元素标签
                            if bearing_idx in bearing_idx_to_elem_tag:
                                bearing_info['elem_tag'] = bearing_idx_to_elem_tag[bearing_idx]

                            bearing_data[time].append(bearing_info)
                            row_count += 1

                        except (ValueError, IndexError) as e:
                            # 跳过无法解析的行
                            continue

                    print(f"成功读取 {row_count} 行支座数据")
                    break

            except UnicodeDecodeError:
                continue

        if not file_opened:
            print("无法使用任何编码打开文件")
            return {}

    except Exception as e:
        print(f"读取支座相对位移数据时出错: {e}")
        return {}

    return bearing_data

