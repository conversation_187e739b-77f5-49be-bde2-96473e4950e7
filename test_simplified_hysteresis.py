"""
测试简化后的滞回曲线绘制功能

该脚本用于测试使用recorder数据的简化滞回曲线绘制功能

作者: <PERSON><PERSON><PERSON> Xu
日期: 2025-08-16
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.hysteresis_plotter import HysteresisPlotter, plot_seismic_hysteresis


def test_bearing_force_loading():
    """测试支座力数据加载"""
    print("=" * 50)
    print("测试支座力数据加载")
    print("=" * 50)
    
    plotter = HysteresisPlotter('results')
    
    # 测试不同的支座索引
    for bearing_idx in [0, 20, 50]:
        print(f"\n测试支座 {bearing_idx}:")
        hysteresis_data = plotter.load_bearing_force_data(bearing_idx)
        
        if hysteresis_data:
            print(f"✓ 成功加载支座 {bearing_idx} 数据")
            print(f"  时间步数: {len(hysteresis_data['time'])}")
            print(f"  水平力范围: {hysteresis_data['force_h'].min():.2f} - {hysteresis_data['force_h'].max():.2f} N")
            print(f"  水平变形范围: {hysteresis_data['deform_h'].min():.6f} - {hysteresis_data['deform_h'].max():.6f} m")
        else:
            print(f"✗ 未找到支座 {bearing_idx} 的力数据")


def test_bearing_hysteresis_plotting():
    """测试支座滞回曲线绘制"""
    print("\n" + "=" * 50)
    print("测试支座滞回曲线绘制")
    print("=" * 50)
    
    plotter = HysteresisPlotter('results')
    
    # 测试支座50（根据示例脚本中的设置）
    bearing_idx = 50
    
    try:
        save_path = f'results/test_simplified_bearing_{bearing_idx}_hysteresis.png'
        plotter.plot_bearing_hysteresis(
            bearing_idx=bearing_idx,
            save_path=save_path,
            show_plot=False
        )
        print(f"✓ 支座 {bearing_idx} 滞回曲线绘制成功")
        print(f"  保存路径: {save_path}")
    except Exception as e:
        print(f"✗ 支座 {bearing_idx} 滞回曲线绘制失败: {e}")


def test_pier_hysteresis_plotting():
    """测试桥墩滞回曲线绘制"""
    print("\n" + "=" * 50)
    print("测试桥墩滞回曲线绘制")
    print("=" * 50)
    
    plotter = HysteresisPlotter('results')
    
    # 查找桥墩数据文件
    pier_files = []
    for file in os.listdir('results'):
        if file.startswith('pier_moment_') and file.endswith('.txt'):
            pier_id = file.replace('pier_moment_', '').replace('.txt', '')
            pier_files.append(pier_id)
    
    if pier_files:
        pier_id = pier_files[0]
        print(f"测试桥墩: {pier_id}")
        
        # 加载桥墩数据
        pier_data = plotter.load_pier_data(pier_id)
        
        if pier_data:
            print(f"✓ 成功加载桥墩数据")
            
            # 只测试Y方向（根据示例脚本中的设置）
            for direction in ['y']:
                try:
                    save_path = f'results/test_simplified_pier_{pier_id}_{direction}_hysteresis.png'
                    plotter.plot_pier_hysteresis(
                        pier_id=pier_id,
                        direction=direction,
                        save_path=save_path,
                        show_plot=False
                    )
                    print(f"✓ 桥墩 {pier_id} {direction.upper()}方向滞回曲线绘制成功")
                    print(f"  保存路径: {save_path}")
                except Exception as e:
                    print(f"✗ 桥墩 {pier_id} {direction.upper()}方向滞回曲线绘制失败: {e}")
        else:
            print(f"✗ 桥墩 {pier_id} 数据加载失败")
    else:
        print("✗ 未找到桥墩数据文件")


def test_comprehensive_plotting():
    """测试综合滞回曲线绘制"""
    print("\n" + "=" * 50)
    print("测试综合滞回曲线绘制")
    print("=" * 50)
    
    plotter = HysteresisPlotter('results')
    
    # 查找桥墩数据
    pier_files = []
    for file in os.listdir('results'):
        if file.startswith('pier_moment_') and file.endswith('.txt'):
            pier_id = file.replace('pier_moment_', '').replace('.txt', '')
            pier_files.append(pier_id)
    
    pier_id = pier_files[0] if pier_files else None
    bearing_idx = 50  # 根据示例脚本设置
    
    if pier_id:
        plotter.load_pier_data(pier_id)
    
    try:
        plotter.plot_comprehensive_hysteresis(
            bearing_idx=bearing_idx,
            pier_id=pier_id,
            save_dir='results'
        )
        print(f"✓ 综合滞回曲线绘制成功")
        print(f"  支座索引: {bearing_idx}")
        print(f"  桥墩ID: {pier_id}")
    except Exception as e:
        print(f"✗ 综合滞回曲线绘制失败: {e}")


def test_convenience_function():
    """测试便捷函数"""
    print("\n" + "=" * 50)
    print("测试便捷函数")
    print("=" * 50)
    
    # 查找桥墩数据
    pier_files = []
    for file in os.listdir('results'):
        if file.startswith('pier_moment_') and file.endswith('.txt'):
            pier_id = file.replace('pier_moment_', '').replace('.txt', '')
            pier_files.append(pier_id)
    
    pier_id = pier_files[0] if pier_files else None
    bearing_idx = 50
    
    try:
        plot_seismic_hysteresis(
            results_dir='results',
            bearing_idx=bearing_idx,
            pier_id=pier_id
        )
        print(f"✓ 便捷函数调用成功")
        print(f"  支座索引: {bearing_idx}")
        print(f"  桥墩ID: {pier_id}")
    except Exception as e:
        print(f"✗ 便捷函数调用失败: {e}")


def check_results():
    """检查生成的结果文件"""
    print("\n" + "=" * 50)
    print("检查生成的结果文件")
    print("=" * 50)
    
    result_files = []
    for file in os.listdir('results'):
        if 'test_simplified' in file and file.endswith('.png'):
            result_files.append(file)
        elif file in ['comprehensive_hysteresis.png', 'bearing_50_hysteresis.png']:
            result_files.append(file)
    
    if result_files:
        print(f"✓ 生成了 {len(result_files)} 个测试图片:")
        for file in sorted(result_files):
            print(f"  - {file}")
        
        print(f"\n请检查这些图片文件，确认:")
        print("1. 支座滞回曲线显示真实的力-位移关系（不再为0）")
        print("2. 桥墩滞回曲线显示正常的弯矩-曲率关系")
        print("3. 图表标签和单位正确显示")
        print("4. 数据范围合理")
    else:
        print("✗ 未找到测试生成的图片文件")


def main():
    """主测试函数"""
    print("简化滞回曲线绘制功能测试")
    print("作者: Zekun Xu")
    print("日期: 2025-08-16")
    
    try:
        # 1. 测试支座力数据加载
        test_bearing_force_loading()
        
        # 2. 测试支座滞回曲线绘制
        test_bearing_hysteresis_plotting()
        
        # 3. 测试桥墩滞回曲线绘制
        test_pier_hysteresis_plotting()
        
        # 4. 测试综合滞回曲线绘制
        test_comprehensive_plotting()
        
        # 5. 测试便捷函数
        test_convenience_function()
        
        # 6. 检查结果
        check_results()
        
        print("\n" + "=" * 50)
        print("测试完成!")
        print("=" * 50)
        
        print("\n主要改进:")
        print("1. ✓ 移除了复杂的支座力计算代码")
        print("2. ✓ 直接使用recorder记录的支座力数据")
        print("3. ✓ 简化了函数参数，移除了model依赖")
        print("4. ✓ 避免了params中不存在属性的引用")
        print("5. ✓ 代码更简洁，逻辑更清晰")
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
