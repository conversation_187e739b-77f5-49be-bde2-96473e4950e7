
# 中文字体安装指南

## Windows系统
1. 系统通常自带中文字体（微软雅黑、宋体等）
2. 如果缺少字体，可以：
   - 控制面板 → 字体 → 安装新字体
   - 下载字体文件(.ttf)并双击安装

## macOS系统
1. 系统自带PingFang SC等中文字体
2. 如需安装新字体：
   - 字体册 → 添加字体
   - 下载.ttf文件并双击安装

## Linux系统
1. 安装中文字体包：
   ```bash
   # Ubuntu/Debian
   sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei
   
   # CentOS/RHEL
   sudo yum install wqy-microhei-fonts wqy-zenhei-fonts
   ```

2. 更新字体缓存：
   ```bash
   fc-cache -fv
   ```

## 验证字体安装
运行本测试脚本检查字体可用性：
```bash
python test_chinese_font.py
```

## 备选方案
如果无法安装中文字体，系统会自动使用英文标签，不影响功能使用。
