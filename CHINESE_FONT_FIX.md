# 中文字体显示问题修复指南

## 问题描述
在使用滞回曲线绘制模块时，可能遇到中文标签无法正常显示的问题，表现为：
- 中文字符显示为方框 □
- 中文字符显示为问号 ?
- 图表标签完全不显示

## 解决方案

### 🔧 自动修复（推荐）
系统已集成智能字体检测和自适应功能：

1. **运行字体检测**：
   ```bash
   python test_chinese_font.py
   ```

2. **查看检测结果**：
   - ✓ 如果找到中文字体，将自动使用中文标签
   - ⚠ 如果未找到中文字体，将自动使用英文标签

3. **直接使用**：
   ```python
   from utils.hysteresis_plotter import HysteresisPlotter
   
   # 创建绘图器（自动处理字体问题）
   plotter = HysteresisPlotter('results')
   ```

### 🛠️ 手动修复

#### Windows系统
1. **检查系统字体**：
   - 打开 控制面板 → 字体
   - 确认存在：微软雅黑、宋体、黑体等

2. **安装缺失字体**：
   - 下载中文字体文件（.ttf格式）
   - 双击字体文件 → 点击"安装"
   - 重启Python程序

#### macOS系统
1. **检查系统字体**：
   - 打开 字体册 应用
   - 确认存在：PingFang SC、Heiti SC等

2. **安装新字体**：
   - 字体册 → 文件 → 添加字体
   - 选择字体文件并安装

#### Linux系统
1. **安装中文字体包**：
   ```bash
   # Ubuntu/Debian
   sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei
   
   # CentOS/RHEL
   sudo yum install wqy-microhei-fonts wqy-zenhei-fonts
   
   # Arch Linux
   sudo pacman -S wqy-microhei wqy-zenhei
   ```

2. **更新字体缓存**：
   ```bash
   fc-cache -fv
   ```

3. **验证安装**：
   ```bash
   fc-list :lang=zh
   ```

## 验证修复效果

### 1. 运行测试脚本
```bash
python test_chinese_font.py
```

### 2. 运行演示程序
```bash
python demo_hysteresis_fixed.py
```

### 3. 检查输出
- 查看控制台输出的字体信息
- 检查生成的图片文件
- 确认中文标签正常显示

## 技术原理

### 智能字体检测机制
```python
def _setup_chinese_font(self):
    """智能检测和设置中文字体"""
    import matplotlib.font_manager as fm
    import platform
    
    # 根据操作系统选择字体候选列表
    system = platform.system()
    if system == "Windows":
        fonts = ['Microsoft YaHei', 'SimHei', 'SimSun']
    elif system == "Darwin":  # macOS
        fonts = ['PingFang SC', 'Heiti SC', 'STHeiti']
    else:  # Linux
        fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans']
    
    # 检测可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]
    
    # 设置第一个可用的中文字体
    for font in fonts:
        if font in available_fonts:
            plt.rcParams['font.sans-serif'] = [font, 'DejaVu Sans']
            return True
    
    # 如果没有中文字体，使用英文
    self.use_english = True
    return False
```

### 自适应标签系统
```python
def _get_labels(self, key):
    """根据字体可用性返回中文或英文标签"""
    chinese_labels = {
        'displacement': '相对位移 (mm)',
        'force': '恢复力 (kN)',
        # ...
    }
    
    english_labels = {
        'displacement': 'Relative Displacement (mm)',
        'force': 'Restoring Force (kN)',
        # ...
    }
    
    if self.use_english:
        return english_labels.get(key, key)
    else:
        return chinese_labels.get(key, key)
```

## 常见问题

### Q1: 安装了中文字体但仍显示异常
**A**: 
1. 重启Python程序
2. 清除matplotlib缓存：
   ```python
   import matplotlib
   matplotlib.font_manager._rebuild()
   ```

### Q2: Linux系统字体安装后不生效
**A**: 
1. 更新字体缓存：`fc-cache -fv`
2. 检查字体路径：`fc-list :lang=zh`
3. 重启终端和Python程序

### Q3: 想强制使用英文标签
**A**: 
```python
plotter = HysteresisPlotter('results')
plotter.use_english = True  # 强制使用英文
```

### Q4: 自定义字体设置
**A**: 
```python
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['Your Font Name', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
```

## 支持的字体列表

### Windows
- Microsoft YaHei (微软雅黑) ✓
- SimHei (黑体) ✓
- SimSun (宋体) ✓
- KaiTi (楷体) ✓

### macOS
- PingFang SC ✓
- Heiti SC ✓
- STHeiti ✓
- Arial Unicode MS ✓

### Linux
- WenQuanYi Micro Hei (文泉驿微米黑) ✓
- WenQuanYi Zen Hei (文泉驿正黑) ✓
- Noto Sans CJK SC ✓

## 备选方案

如果无法解决中文字体问题，系统提供以下备选方案：

1. **自动英文回退**：系统自动使用英文标签
2. **手动标签设置**：可以手动修改标签文本
3. **后期编辑**：使用图像编辑软件添加中文标注

## 技术支持

如果仍有问题，请：
1. 运行 `python test_chinese_font.py` 获取详细诊断信息
2. 查看控制台输出的字体检测结果
3. 检查系统字体安装情况
4. 参考本指南进行相应修复

---

**注意**：字体问题不影响滞回曲线绘制的核心功能，只是影响标签显示语言。所有计算和绘图功能都能正常工作。
