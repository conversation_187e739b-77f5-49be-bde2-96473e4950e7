# 地震时程分析滞回曲线绘制系统

## 系统概述

本系统提供专业的地震时程分析滞回曲线绘制功能，专门用于桥梁工程的抗震分析。系统采用模块化设计，支持多种支座类型和桥墩响应分析，符合工程实践需求。

## 主要功能

### 1. 支座滞回曲线分析
- **力-位移滞回曲线**：展示支座在地震作用下的恢复力特性
- **多种支座类型**：橡胶支座、摩擦支座、双线性支座
- **专业物理量**：
  - 相对位移 (mm)
  - 恢复力 (kN)
  - 初始刚度 (MN/m)
  - 能量耗散 (kN·mm)
  - 等效阻尼比

### 2. 桥墩滞回曲线分析
- **弯矩-曲率滞回曲线**：反映桥墩底部塑性铰发展
- **专业物理量**：
  - 曲率 (1/km)
  - 弯矩 (MN·m)
  - 累积耗散能量 (MN·m)
- **双向分析**：X方向和Y方向独立分析

## 系统架构

```
utils/
├── hysteresis_plotter.py          # 主绘图模块
├── bearing_force_calculator.py    # 支座力计算模块
└── bearing_relative_displacement_save.py  # 数据保存模块

examples/
└── plot_hysteresis_example.py     # 使用示例

docs/
└── hysteresis_plotting_guide.md   # 详细使用指南

test_hysteresis_plotting.py        # 测试脚本
```

## 核心模块

### 1. HysteresisPlotter 类
主要绘图类，提供以下方法：
- `load_bearing_data()`: 加载支座数据
- `load_pier_data()`: 加载桥墩数据
- `plot_bearing_hysteresis()`: 绘制支座滞回曲线
- `plot_pier_hysteresis()`: 绘制桥墩滞回曲线
- `plot_comprehensive_hysteresis()`: 绘制综合分析图

### 2. BearingForceCalculator 类
专业支座力计算器，支持：
- 橡胶支座双线性本构
- 摩擦支座弹塑性行为
- 轴向荷载影响修正
- 滞回特性参数计算

## 快速开始

### 1. 基本使用
```python
from utils.hysteresis_plotter import plot_seismic_hysteresis
from params import BridgeParams
from core.simply_supported_beam_model import SimplySupportedBeamModel

# 创建桥梁模型
params = BridgeParams()
model = SimplySupportedBeamModel(params)

# 一键绘制滞回曲线
plot_seismic_hysteresis(
    results_dir='results',
    bearing_idx=0,                    # 第一个支座
    pier_id='pier_x15.0_y0.0',       # 第一个桥墩
    model=model
)
```

### 2. 高级使用
```python
from utils.hysteresis_plotter import HysteresisPlotter

# 创建专业绘图器
plotter = HysteresisPlotter('results')

# 加载数据
plotter.load_bearing_data(model=model)
plotter.load_pier_data('pier_x15.0_y0.0')

# 专业绘图
plotter.plot_bearing_hysteresis(
    bearing_idx=0,
    bearing_type='bilinear',  # 指定支座类型
    save_path='bearing_hysteresis.png'
)
```

## 输出结果

### 图形文件
1. **支座滞回曲线图**
   - 主滞回曲线（力-位移）
   - 位移时程
   - 恢复力时程
   - 滞回特性参数

2. **桥墩滞回曲线图**
   - 弯矩-曲率滞回曲线
   - 弯矩时程
   - 曲率时程

3. **综合分析图**
   - 支座和桥墩滞回曲线
   - 能量耗散分析
   - 多方向响应对比

### 工程参数
- **初始刚度**：结构弹性特性
- **能量耗散**：结构耗能能力
- **等效阻尼比**：减震效果评估
- **最大响应**：极限状态验算

## 工程应用

### 1. 支座设计验证
- 支座刚度合理性检查
- 支座位移限值验算
- 支座耗能贡献评估

### 2. 桥墩抗震性能评估
- 塑性铰发展分析
- 延性需求评估
- 损伤状态判断

### 3. 整体抗震性能评价
- 结构耗能机制分析
- 抗震薄弱环节识别
- 抗震设计优化建议

## 技术特点

### 1. 专业性
- 符合桥梁抗震设计规范
- 采用工程常用物理量和单位
- 考虑实际工程约束条件

### 2. 准确性
- 基于OpenSees分析结果
- 考虑支座轴向荷载影响
- 采用专业本构关系模型

### 3. 易用性
- 模块化设计，便于扩展
- 提供便捷函数和详细示例
- 自动化数据处理和图形生成

### 4. 可视化
- 专业的工程图表样式
- 清晰的物理量标注
- 丰富的分析信息展示

### 5. 国际化支持
- **智能字体检测**：自动检测系统可用的中文字体
- **中英文自适应**：根据字体可用性自动切换中英文标签
- **跨平台兼容**：支持Windows、macOS、Linux系统
- **字体回退机制**：中文字体不可用时自动使用英文标签

## 系统要求

### 依赖库
```python
numpy >= 1.20.0
matplotlib >= 3.3.0
```

### 数据要求
- 地震时程分析结果文件
- 支座相对位移数据 (CSV格式)
- 桥墩响应数据 (TXT格式)

## 测试验证

### 1. 中文字体测试
检测系统中文字体支持情况：
```bash
python test_chinese_font.py
```

### 2. 功能演示
运行完整功能演示：
```bash
python demo_hysteresis_fixed.py
```

### 3. 字体问题解决
如果遇到中文显示问题：
1. 运行字体测试脚本检查可用字体
2. 参考生成的 `font_installation_guide.md` 安装中文字体
3. 系统会自动回退到英文标签，不影响功能使用

测试内容包括：
- 中文字体可用性检测
- 支座力计算器验证
- 滞回特性参数计算
- 绘图功能完整性测试
- 中英文标签自适应

## 扩展开发

### 1. 新增支座类型
在 `BearingForceCalculator` 中添加新的本构关系方法

### 2. 新增分析指标
在滞回特性计算中添加新的工程参数

### 3. 自定义绘图样式
修改 `HysteresisPlotter` 中的matplotlib参数

### 4. 批量处理功能
扩展为多地震动、多结构的批量分析

## 技术支持

### 文档资源
- `docs/hysteresis_plotting_guide.md`: 详细使用指南
- `examples/plot_hysteresis_example.py`: 完整使用示例
- 代码注释：详细的函数和类说明

### 故障排除
- 检查数据文件完整性
- 验证支座参数设置
- 确认文件路径正确性

## 版本信息

- **版本**: 1.0.0
- **作者**: 桥梁工程师
- **日期**: 2025-08-16
- **兼容性**: Python 3.7+, OpenSees 3.0+

## 许可证

本系统遵循项目整体许可证协议。
